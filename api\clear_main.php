<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

try {
    // Limpiar el archivo JSON del historial
    $jsonFile = __DIR__ . '/main_history.json';
    file_put_contents($jsonFile, json_encode(['messages' => []], JSON_PRETTY_PRINT));
    
    echo json_encode([
        'success' => true,
        'message' => 'Chat limpiado correctamente'
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error limpiando chat: ' . $e->getMessage()
    ]);
} 