<?php
// Configurar logging
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/php_errors.log');
error_reporting(E_ALL);

// Función helper para logging con rotación básica
function logError($message, $data = null) {
    $logFile = __DIR__ . '/php_errors.log';
    $maxSize = 10 * 1024 * 1024; // 10MB por defecto

    // Verificar tamaño del archivo y rotar si es necesario
    if (file_exists($logFile) && filesize($logFile) > $maxSize) {
        $backupFile = __DIR__ . '/php_errors_' . date('Y-m-d_H-i-s') . '.log';
        rename($logFile, $backupFile);

        // Mantener solo los últimos 5 archivos de backup
        $backupFiles = glob(__DIR__ . '/php_errors_*.log');
        if (count($backupFiles) > 5) {
            usort($backupFiles, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            unlink($backupFiles[0]);
        }
    }

    $log = date('Y-m-d H:i:s') . " - " . $message;
    if ($data) {
        $log .= " - Data: " . print_r($data, true);
    }
    error_log($log);
}

require __DIR__ . '/../vendor/autoload.php';

use Gemini\Data\Content;
use Gemini\Enums\Role;
use GuzzleHttp\Exception\RequestException;
use Dotenv\Dotenv;

try {
    // Cargar variables de entorno
    $dotenv = Dotenv::createImmutable(__DIR__ . '/..');
    $dotenv->load();
    logError("Variables de entorno cargadas");
} catch (Exception $e) {
    logError("Error cargando .env", $e->getMessage());
}

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
logError("Input recibido", $input);

if (!isset($input['message'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Mensaje no proporcionado']);
    exit;
}

// Validación del mensaje
$message = trim($input['message']);
if (empty($message)) {
    http_response_code(400);
    echo json_encode(['error' => 'El mensaje no puede estar vacío']);
    exit;
}

$maxLength = (int)($_ENV['CHAT_MESSAGE_MAX_LENGTH'] ?? 2000);
if (strlen($message) > $maxLength) {
    http_response_code(400);
    echo json_encode(['error' => "El mensaje es demasiado largo (máximo {$maxLength} caracteres)"]);
    exit;
}

try {
    // Archivo JSON para almacenar mensajes
    $jsonFile = __DIR__ . '/main_history.json';
    
    // Crear archivo si no existe
    if (!file_exists($jsonFile)) {
        file_put_contents($jsonFile, json_encode(['messages' => []]));
    }
    
    // Leer historial existente
    $history = json_decode(file_get_contents($jsonFile), true);
    logError("Historial cargado", $history);

    // Preparar historial para Gemini (usar límite de variables de entorno)
    $historyLimit = (int)($_ENV['CHAT_HISTORY_LIMIT'] ?? 10);
    $geminiHistory = [];
    foreach (array_slice($history['messages'], -$historyLimit) as $msg) {
        if ($msg['is_user']) {
            $geminiHistory[] = Content::parse(part: $msg['message'], role: Role::USER);
        } elseif (!empty($msg['response'])) {
            $geminiHistory[] = Content::parse(part: $msg['response'], role: Role::MODEL);
        }
    }
    $geminiHistory[] = Content::parse(part: $input['message'], role: Role::USER);
    logError("Historial preparado para Gemini", $geminiHistory);

    // Inicializar Gemini usando variables de entorno
    $apiKey = $_ENV['GEMINI_API_KEY'] ?? '';
    $model = $_ENV['GEMINI_MODEL'] ?? 'gemini-1.5-flash';

    if (empty($apiKey)) {
        throw new Exception('API Key de Gemini no configurada');
    }

    logError("Intentando inicializar Gemini con modelo: " . $model);
    $client = Gemini::client($apiKey);
    logError("Cliente Gemini creado");

    $chat = $client->generativeModel($model)->startChat(history: $geminiHistory);
    logError("Chat iniciado");
    
    $response = $chat->sendMessage($message);
    $text = $response->text();
    logError("Respuesta de Gemini recibida", $text);

    if (!$text) {
        throw new Exception('La respuesta de la API no contiene texto.');
    }

    // Guardar mensajes en JSON
    $history['messages'][] = [
        'message' => $message,
        'is_user' => true,
        'created_at' => date('Y-m-d H:i:s')
    ];
    $history['messages'][] = [
        'message' => '',
        'response' => $text,
        'is_user' => false,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    file_put_contents($jsonFile, json_encode($history, JSON_PRETTY_PRINT));
    logError("Mensajes guardados en JSON");

    echo json_encode([
        'success' => true,
        'response' => $text
    ]);
} catch (RequestException $e) {
    logError("Error de conexión con Gemini", $e->getMessage());
    http_response_code(503);
    echo json_encode([
        'success' => false,
        'error' => 'Error de conexión con Gemini: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    logError("Error inesperado", $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error inesperado: ' . $e->getMessage()
    ]);
} 