<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HazloLatam Chat</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        dark: {
                            'bg-primary': '#1a1a1a',
                            'bg-secondary': '#2d2d2d',
                            'text-primary': '#ffffff',
                            'text-secondary': '#a0a0a0'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Cursor de escritura personalizado - estilo Claude */
        .typing-cursor {
            display: inline-block;
            color: #60a5fa;
            font-weight: 300;
            animation: pulse 1.5s ease-in-out infinite;
            margin-left: 1px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        /* Animación de aparición suave para mensajes */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Scroll suave para el chat */
        #chat-messages {
            scroll-behavior: smooth;
            /* Centrado perfecto del contenido */
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        /* Asegurar que los mensajes mantengan su ancho apropiado */
        #chat-messages > div {
            width: 100%;
            max-width: 48rem; /* max-w-3xl */
        }

        /* Mejoras visuales para respuestas de IA */
        .ai-response {
            line-height: 1.6;
            letter-spacing: 0.01em;
        }

        .ai-response h1, .ai-response h2, .ai-response h3 {
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }

        .ai-response ul, .ai-response ol {
            margin: 1rem 0;
            padding-left: 1.5rem;
        }

        .ai-response li {
            margin-bottom: 0.5rem;
        }

        /* Animación de aparición suave */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-100 min-h-screen flex transition-colors duration-200">
    <!-- Botón para colapsar sidebar -->
    <button id="sidebar-toggle" class="fixed top-4 left-4 z-50 p-2 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
    </button>

    <!-- Sidebar -->
    <aside id="sidebar" class="w-64 bg-gray-800 flex flex-col justify-between py-6 px-4 min-h-screen fixed transform -translate-x-full transition-transform duration-200 ease-in-out z-40 md:w-72">
        <button id="sidebar-close" class="absolute top-4 right-4 p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
        <div>
            <!-- Contenido cuando está logueado -->
            <div id="logged-in-content" class="hidden">
                <div class="mb-6">
                    <h2 class="text-lg font-semibold mb-4">Chat</h2>
                    <button id="new-chat-btn" class="w-full bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded transition-colors flex items-center gap-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Nuevo chat
                    </button>
                </div>

                <div class="mb-6">
                    <h3 class="text-sm font-medium text-gray-400 mb-3">Enlaces Rápidos</h3>
                    <nav class="space-y-2">
                        <a href="https://hazlolatam.com" target="_blank" class="quick-link flex items-center gap-2 text-gray-300 hover:text-white transition-colors p-2 rounded hover:bg-gray-700">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                            </svg>
                            <span>HazloLatam</span>
                        </a>
                        <a href="https://hazlolatam.com/creadores" target="_blank" class="quick-link flex items-center gap-2 text-gray-300 hover:text-white transition-colors p-2 rounded hover:bg-gray-700">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span>Creadores</span>
                        </a>
                        <a href="https://hazlolatam.com/comunidad" target="_blank" class="quick-link flex items-center gap-2 text-gray-300 hover:text-white transition-colors p-2 rounded hover:bg-gray-700">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <span>Comunidad</span>
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Contenido cuando NO está logueado -->
            <div id="logged-out-content">
                <div class="mb-6 text-center">
                    <h2 class="text-lg font-semibold mb-4 text-gray-300">Bienvenido</h2>
                    <p class="text-sm text-gray-400 mb-4">Inicia sesión para acceder al chat</p>
                </div>
            </div>
        </div>
        
        <div class="space-y-2">
            <!-- Botón cuando está logueado -->
            <button id="logout-btn" class="hidden w-full flex items-center gap-2 text-gray-400 hover:text-white py-2 px-4 rounded transition-colors hover:bg-gray-700">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17 16l4-4m0 0l-4-4m4 4H7"/>
                </svg>
                <span>Cerrar sesión</span>
            </button>

            <!-- Botón cuando NO está logueado -->
            <button id="login-btn" class="w-full flex items-center gap-2 text-white bg-green-600 hover:bg-green-700 py-3 px-4 rounded-lg transition-colors font-semibold">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097"/>
                </svg>
                <span>Iniciar con WhatsApp</span>
            </button>
        </div>
    </aside>

    <!-- Main content -->
    <main class="flex-1 flex flex-col items-center justify-center relative min-h-screen pb-0">
        <div class="w-full max-w-3xl mx-auto flex flex-col items-center mt-16">
            <h1 class="text-4xl font-bold mb-8 text-center">HazloLatam Chat</h1>
            <div id="main-cards" class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full mb-8 transition-all duration-500">
                <button class="rounded-xl p-4 shadow font-semibold text-center w-full bg-gradient-to-br from-blue-400 to-indigo-500 text-white hover:scale-105 hover:shadow-lg transition-all duration-300" data-msg="Quiero hacer un diagnóstico de mi proyecto. ¿Qué información necesitas para comenzar?">Hacer Diagnóstico</button>
                <button class="rounded-xl p-4 shadow font-semibold text-center w-full bg-gradient-to-br from-green-400 to-teal-500 text-white hover:scale-105 hover:shadow-lg transition-all duration-300" data-msg="Quiero crear un roadmap para mi proyecto. ¿Por dónde empiezo?">Crear Roadmap</button>
                <button class="rounded-xl p-4 shadow font-semibold text-center w-full bg-gradient-to-br from-purple-400 to-pink-500 text-white hover:scale-105 hover:shadow-lg transition-all duration-300" data-msg="Me gustaría recibir mentoría. ¿Qué tipo de mentoría me recomiendas según mi perfil?">Recibir Mentoría</button>
                <button class="rounded-xl p-4 shadow font-semibold text-center w-full bg-gradient-to-br from-pink-400 to-red-400 text-white hover:scale-105 hover:shadow-lg transition-all duration-300" data-msg="Estoy buscando un co-founder. ¿Qué debo considerar y cómo empiezo la búsqueda?">Buscar Co-Founder</button>
                <button class="rounded-xl p-4 shadow font-semibold text-center w-full bg-gradient-to-br from-orange-400 to-yellow-400 text-white hover:scale-105 hover:shadow-lg transition-all duration-300" data-msg="Quiero hacer un análisis de mercado para mi idea. ¿Qué pasos debo seguir?">Hacer Análisis de Mercado</button>
                <button class="rounded-xl p-4 shadow font-semibold text-center w-full bg-gradient-to-br from-cyan-400 to-blue-300 text-white hover:scale-105 hover:shadow-lg transition-all duration-300" data-msg="¿Cómo genero un Model Canvas para mi proyecto?">Generar Model Canvas</button>
                <button class="rounded-xl p-4 shadow font-semibold text-center w-full bg-gradient-to-br from-teal-400 to-green-300 text-white hover:scale-105 hover:shadow-lg transition-all duration-300" data-msg="¿Cómo creo un buyer persona efectivo para mi producto?">Crear Buyer Persona</button>
                <button class="rounded-xl p-4 shadow font-semibold text-center w-full bg-gradient-to-br from-yellow-400 to-orange-300 text-white hover:scale-105 hover:shadow-lg transition-all duration-300" data-msg="¿Cómo puedo crear el customer journey de mi cliente ideal?">Crear Costumer Journey</button>
                <button class="rounded-xl p-4 shadow font-semibold text-center w-full bg-gradient-to-br from-indigo-400 to-blue-600 text-white hover:scale-105 hover:shadow-lg transition-all duration-300" data-msg="¿Qué debe incluir un pitch deck para inversores?">Crear Pitch Deck</button>
                <button class="rounded-xl p-4 shadow font-semibold text-center w-full bg-gradient-to-br from-red-400 to-pink-500 text-white hover:scale-105 hover:shadow-lg transition-all duration-300" data-msg="¿Cómo hago un business plan sólido para mi startup?">Crear un Business Plan</button>
                <button class="rounded-xl p-4 shadow font-semibold text-center w-full bg-gradient-to-br from-gray-400 to-gray-700 text-white hover:scale-105 hover:shadow-lg transition-all duration-300" data-msg="¿Cómo valido mi idea de negocio antes de invertir más tiempo y dinero?">Validar Idea</button>
                <button class="rounded-xl p-4 shadow font-semibold text-center w-full bg-gradient-to-br from-lime-400 to-green-500 text-white hover:scale-105 hover:shadow-lg transition-all duration-300" data-msg="¿Cuáles son los pasos para crear un MVP funcional?">Crear MVP</button>
            </div>
            <!-- Input centrado, con clase para animación -->
            <div id="main-input-box" class="w-full max-w-2xl flex gap-2 px-2 mb-8 transition-all duration-500 justify-center">
                <input type="text" id="message-input" class="flex-1 p-3 rounded-lg bg-gray-800 text-white border border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Escribe tu mensaje...">
                <button id="send-button" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">Enviar</button>
            </div>
        </div>
        <!-- Chat messages - Centrado y con mejor espaciado -->
        <div id="chat-messages" class="w-full max-w-4xl mx-auto flex-1 overflow-y-auto px-4 pb-20 hidden"></div>
        <!-- Animación de esperando respuesta -->
        <div id="waiting-response" class="w-full max-w-4xl mx-auto flex justify-center items-center mb-4 hidden">
            <div class="flex items-center gap-2 text-blue-400 font-semibold animate-pulse">
                <span>Esperando respuesta</span>
                <span class="dot-1">.</span><span class="dot-2">.</span><span class="dot-3">.</span>
            </div>
        </div>
    </main>

    <!-- Modal de Login -->
    <div id="login-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-gray-800 rounded-lg p-8 max-w-md w-full mx-4 border border-gray-700">
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097"/>
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-white mb-2">Iniciar Sesión</h2>
                <p class="text-gray-400">Ingresa tu número de WhatsApp para continuar</p>
            </div>

            <form id="login-form" class="space-y-4">
                <div>
                    <label for="phone-number" class="block text-sm font-medium text-gray-300 mb-2">Número de WhatsApp</label>
                    <div class="relative">
                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">+</span>
                        <input type="tel" id="phone-number" class="w-full pl-8 pr-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="1234567890" required>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Ejemplo: 573001234567 (código de país + número)</p>
                </div>

                <div class="flex gap-3 pt-4">
                    <button type="button" id="cancel-login" class="flex-1 py-3 px-4 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
                        Cancelar
                    </button>
                    <button type="submit" class="flex-1 py-3 px-4 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors font-semibold">
                        Continuar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Sidebar toggle
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebar-toggle');
        
        sidebarToggle.addEventListener('click', () => {
            sidebar.classList.toggle('-translate-x-full');
        });

        // Tema oscuro por defecto (sin toggle)
        const html = document.documentElement;
        html.classList.add('dark');

        // La funcionalidad de limpiar chat ahora está manejada en ui.js
    </script>
    <script src="js/auth.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/main.js"></script>
</body>
</html> 