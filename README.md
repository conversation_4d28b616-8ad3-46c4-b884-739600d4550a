# HazloLatam Chat

Un clon de ChatGPT con integración a HazloLatam, construido con PHP, JavaScript y Tailwind CSS.

## Requisitos

- PHP 7.4 o superior
- MySQL 5.7 o superior
- Servidor web (Apache/Nginx)
- Composer (opcional, para futuras dependencias)

## Instalación

1. Clona este repositorio en tu servidor web:
```bash
git clone [URL_DEL_REPOSITORIO]
```

2. Crea la base de datos y las tablas necesarias:
```bash
mysql -u root -p < database.sql
```

3. Configura la base de datos:
   - Abre `api/main.php`
   - Modifica las credenciales de la base de datos según tu configuración

4. Asegúrate de que los permisos de los directorios sean correctos:
```bash
chmod 755 -R .
chmod 777 -R api/
```

5. Accede a la aplicación a través de tu navegador:
```
http://localhost/[ruta-del-proyecto]
```

## Estructura del Proyecto

```
├── api/
│   ├── main.php
│   ├── clear_main.php
│   └── main_history.json
├── js/
│   └── main.js
├── index.php
├── database.sql
└── README.md
```

## Características

- Interfaz de chat moderna y responsiva
- Integración con HazloLatam
- Almacenamiento de conversaciones en base de datos
- Diseño adaptable a diferentes dispositivos
- Interfaz de usuario intuitiva

## Contribuir

1. Haz un Fork del proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo LICENSE para más detalles. 