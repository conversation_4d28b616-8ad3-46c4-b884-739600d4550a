document.addEventListener('DOMContentLoaded', () => {
    // Sidebar colapsable
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebarClose = document.getElementById('sidebar-close');
    let overlay = null;
    function openSidebar() {
        sidebar.classList.remove('-translate-x-full');
        // Crear overlay solo si no existe
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'fixed inset-0 bg-black bg-opacity-40 z-30';
            overlay.addEventListener('click', closeSidebar);
            document.body.appendChild(overlay);
        }
    }
    function closeSidebar() {
        sidebar.classList.add('-translate-x-full');
        if (overlay) {
            overlay.remove();
            overlay = null;
        }
    }
    if (sidebarToggle) sidebarToggle.addEventListener('click', openSidebar);
    if (sidebarClose) sidebarClose.addEventListener('click', closeSidebar);
    // Cerrar sidebar al hacer clic en un enlace o botón (en cualquier tamaño)
    if (sidebar) {
        sidebar.querySelectorAll('a,button').forEach(el => {
            el.addEventListener('click', () => {
                if (el !== sidebarClose) closeSidebar();
            });
        });
    }
    // Cerrar sidebar con Esc
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') closeSidebar();
    });

    // Limpiar chat y ocultar caja de mensajes
    const newChatBtn = document.getElementById('new-chat-btn');

    function clearChatHistory() {
        // Limpiar historial en el servidor
        fetch('api/clear_main.php', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Chat limpiado correctamente');
                } else {
                    console.error('Error limpiando chat:', data.error);
                }
            })
            .catch(error => console.error('Error limpiando chat:', error));

        // Disparar evento para limpiar UI
        window.dispatchEvent(new Event('clearChat'));
    }

    if (newChatBtn) {
        newChatBtn.addEventListener('click', clearChatHistory);
    }
}); 