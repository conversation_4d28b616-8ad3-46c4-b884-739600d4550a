document.addEventListener('DOMContentLoaded', () => {
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');
    const chatMessages = document.getElementById('chat-messages');
    const mainCards = document.getElementById('main-cards');
    const mainInputBox = document.getElementById('main-input-box');
    const waitingResponse = document.getElementById('waiting-response');

    // Función para scroll suave mejorada
    function smoothScrollToBottom(force = false) {
        if (chatMessages) {
            // Si force es true, hacer scroll inmediatamente
            if (force) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
            } else {
                // Scroll suave normal
                chatMessages.scrollTo({
                    top: chatMessages.scrollHeight,
                    behavior: 'smooth'
                });
            }
        }
    }

    // Mostrar la caja de mensajes solo cuando hay mensajes
    function showChatBox() {
        chatMessages.classList.remove('hidden');
        // Agregar el fondo solo cuando se muestra el chat
        chatMessages.classList.add('bg-[#18181b]');
    }
    function hideChatBox() {
        chatMessages.classList.add('hidden');
        // Remover el fondo cuando se oculta
        chatMessages.classList.remove('bg-[#18181b]');
    }
    hideChatBox(); // Ocultar al inicio

    // Formatear respuesta de IA con markdown mejorado
    function formatAIResponse(text) {
        // Limpiar texto inicial
        text = text.trim();

        // Convertir títulos
        text = text.replace(/^### (.*$)/gm, '<h3 class="text-lg font-semibold mt-4 mb-2 text-blue-400">$1</h3>');
        text = text.replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold mt-4 mb-2 text-blue-400">$1</h2>');
        text = text.replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mt-4 mb-2 text-blue-400">$1</h1>');

        // Convertir negritas y cursivas
        text = text.replace(/\*\*([^*]+)\*\*/g, '<strong class="font-semibold text-white">$1</strong>');
        text = text.replace(/\*([^*]+)\*/g, '<em class="italic text-gray-300">$1</em>');

        // Convertir listas con viñetas
        text = text.replace(/^\* (.+)$/gm, '<li class="ml-4 mb-1">• $1</li>');
        text = text.replace(/^- (.+)$/gm, '<li class="ml-4 mb-1">• $1</li>');

        // Convertir listas numeradas
        text = text.replace(/^\d+\. (.+)$/gm, '<li class="ml-4 mb-1 list-decimal">$1</li>');

        // Agrupar listas consecutivas
        text = text.replace(/(<li[^>]*>.*?<\/li>\s*)+/gs, '<ul class="mb-3">$&</ul>');

        // Convertir párrafos (doble salto de línea)
        text = text.replace(/\n\n+/g, '</p><p class="mb-3 leading-relaxed">');

        // Convertir saltos de línea simples
        text = text.replace(/\n/g, '<br>');

        // Envolver en párrafo inicial
        text = `<p class="mb-3 leading-relaxed">${text}</p>`;

        // Limpiar párrafos vacíos
        text = text.replace(/<p[^>]*><\/p>/g, '');

        return text;
    }

    // Animación: ocultar tarjetas y mover input abajo
    function animateToChatMode() {
        if (mainCards) {
            mainCards.classList.add('opacity-0', 'pointer-events-none');
            setTimeout(() => mainCards.classList.add('hidden'), 500);
        }
        if (mainInputBox) {
            mainInputBox.classList.remove('mb-8');
            mainInputBox.classList.add('fixed', 'bottom-0', 'left-1/2', 'transform', '-translate-x-1/2', 'justify-center', 'bg-[#18181b]', 'pb-4', 'pt-2', 'z-20');
            mainInputBox.classList.add('transition-all', 'duration-500');
            // Mantener el mismo ancho que tenía con las tarjetas (max-w-2xl) pero con padding
            mainInputBox.style.maxWidth = '42rem'; // equivalente a max-w-2xl
            mainInputBox.style.width = 'calc(100% - 2rem)'; // Con padding lateral
            mainInputBox.style.paddingLeft = '1rem';
            mainInputBox.style.paddingRight = '1rem';
        }
    }

    // Animación de esperando respuesta
    let waitingInterval = null;
    function showWaiting() {
        if (waitingResponse) {
            waitingResponse.classList.remove('hidden');
            // Animación de puntos
            let dots = 0;
            waitingInterval = setInterval(() => {
                dots = (dots + 1) % 4;
                waitingResponse.querySelector('span.dot-1').style.opacity = dots > 0 ? 1 : 0.2;
                waitingResponse.querySelector('span.dot-2').style.opacity = dots > 1 ? 1 : 0.2;
                waitingResponse.querySelector('span.dot-3').style.opacity = dots > 2 ? 1 : 0.2;
            }, 400);
        }
    }
    function hideWaiting() {
        if (waitingResponse) waitingResponse.classList.add('hidden');
        if (waitingInterval) clearInterval(waitingInterval);
        waitingInterval = null;
    }

    // Función para agregar un mensaje al chat (usuario: burbuja azul, IA: bloque estructurado)
    function addMessage(message, isUser = false) {
        showChatBox();
        const messageDiv = document.createElement('div');
        messageDiv.className = `mb-4 w-full flex ${isUser ? 'justify-end' : 'justify-start'}`;
        
        if (isUser) {
            const bubble = document.createElement('div');
            bubble.className = 'inline-block p-3 rounded-2xl bg-blue-600 text-white max-w-[80%] shadow-md';
            bubble.textContent = message;
            messageDiv.appendChild(bubble);
            chatMessages.appendChild(messageDiv);
            smoothScrollToBottom();
        } else {
            // IA: bloque estructurado con estilo mejorado y centrado
            const aiContainer = document.createElement('div');
            aiContainer.className = 'w-full flex justify-center';

            const aiBlock = document.createElement('div');
            aiBlock.className = 'ai-response w-full max-w-3xl px-6 py-4 text-left text-gray-100 font-normal bg-gray-800 rounded-lg border-l-4 border-blue-500 shadow-md fade-in mx-auto';
            aiBlock.style.wordBreak = 'break-word';
            aiBlock.innerHTML = '';

            aiContainer.appendChild(aiBlock);
            messageDiv.appendChild(aiContainer);
            chatMessages.appendChild(messageDiv);

            // Efecto de escritura suave carácter por carácter
            const cleanText = formatAIResponse(message);
            typeWriterSmooth(aiBlock, cleanText);
        }
    }

    // Efecto de escritura suave mejorado - similar al de Claude
    function typeWriterSmooth(element, html, speed = 25) {
        // Extraer solo el texto plano del HTML para el efecto de escritura
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        const plainText = tempDiv.textContent || tempDiv.innerText || '';

        let currentIndex = 0;
        let displayText = '';
        let isTyping = true;

        function typeNextChar() {
            if (currentIndex < plainText.length && isTyping) {
                displayText += plainText[currentIndex];
                currentIndex++;

                // Aplicar formato básico en tiempo real
                let formattedText = displayText;
                formattedText = formattedText.replace(/\*\*([^*]+)\*\*/g, '<strong class="font-semibold text-white">$1</strong>');
                formattedText = formattedText.replace(/\*([^*]+)\*/g, '<em class="italic text-gray-300">$1</em>');
                formattedText = formattedText.replace(/\n/g, '<br>');

                // Mostrar texto con cursor parpadeante más sutil
                element.innerHTML = formattedText + '<span class="typing-cursor animate-pulse">|</span>';

                // Auto-scroll continuo y suave
                autoScrollToBottom();

                // Velocidad variable más natural - similar a Claude
                let nextSpeed = speed;
                const currentChar = plainText[currentIndex - 1];

                // Más rápido en espacios (flujo natural)
                if (currentChar === ' ') {
                    nextSpeed = speed * 0.3;
                }
                // Pausa larga en puntos (final de oración)
                else if (currentChar === '.') {
                    nextSpeed = speed * 4;
                }
                // Pausa media en signos de interrogación y exclamación
                else if (['!', '?'].includes(currentChar)) {
                    nextSpeed = speed * 2.5;
                }
                // Pausa corta en comas y otros signos
                else if ([',', ';', ':'].includes(currentChar)) {
                    nextSpeed = speed * 1.8;
                }
                // Pausa en saltos de línea (cambio de párrafo)
                else if (currentChar === '\n') {
                    nextSpeed = speed * 3;
                }
                // Variación aleatoria sutil para naturalidad
                else {
                    nextSpeed = speed + (Math.random() - 0.5) * speed * 0.3;
                }

                setTimeout(typeNextChar, nextSpeed);
            } else {
                // Finalizar con el HTML completo formateado (sin cursor)
                element.innerHTML = html;
                smoothScrollToBottom(true); // Scroll forzado al final
                isTyping = false;
            }
        }

        // Permitir cancelar el efecto si el usuario hace scroll manual
        let userScrolled = false;
        const chatContainer = document.getElementById('chat-messages');

        const handleScroll = () => {
            const isAtBottom = chatContainer.scrollTop + chatContainer.clientHeight >= chatContainer.scrollHeight - 10;
            if (!isAtBottom && isTyping) {
                userScrolled = true;
            }
        };

        chatContainer.addEventListener('scroll', handleScroll);

        // Función de scroll local que respeta el scroll del usuario
        function autoScrollToBottom() {
            if (!userScrolled && isTyping) {
                chatContainer.scrollTo({
                    top: chatContainer.scrollHeight,
                    behavior: 'smooth'
                });
            }
        }

        typeNextChar();

        // Limpiar event listener cuando termine
        setTimeout(() => {
            chatContainer.removeEventListener('scroll', handleScroll);
        }, plainText.length * speed + 1000);
    }

    // Función para enviar mensaje al servidor
    async function sendMessage(message) {
        try {
            showWaiting();
            const response = await fetch('api/main.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            hideWaiting();

            if (data.success) {
                addMessage(data.response);
            } else {
                const errorMsg = data.error || 'Error desconocido al procesar tu mensaje';
                addMessage(`Lo siento, hubo un error: ${errorMsg}`);
                console.error('Error del servidor:', data.error);
            }
        } catch (error) {
            hideWaiting();
            console.error('Error de conexión:', error);

            let errorMessage = 'Error de conexión. Por favor, intenta de nuevo.';
            if (error.message.includes('HTTP error')) {
                errorMessage = 'Error del servidor. Por favor, intenta más tarde.';
            } else if (error.name === 'TypeError') {
                errorMessage = 'Error de red. Verifica tu conexión a internet.';
            }

            addMessage(errorMessage);
        }
    }

    // Validación de entrada
    function validateMessage(message) {
        if (!message || message.trim().length === 0) {
            return { valid: false, error: 'El mensaje no puede estar vacío' };
        }
        if (message.length > 2000) {
            return { valid: false, error: 'El mensaje es demasiado largo (máximo 2000 caracteres)' };
        }
        return { valid: true };
    }

    // Event listeners
    sendButton.addEventListener('click', () => {
        const message = messageInput.value.trim();
        const validation = validateMessage(message);

        if (!validation.valid) {
            alert(validation.error);
            return;
        }

        animateToChatMode();
        addMessage(message, true);
        sendMessage(message);
        messageInput.value = '';
    });

    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendButton.click();
        }
    });

    // Tarjetas clickeables para iniciar el chat
    if (mainCards) {
        mainCards.querySelectorAll('button[data-msg]').forEach(btn => {
            btn.addEventListener('click', () => {
                const msg = btn.getAttribute('data-msg');
                const validation = validateMessage(msg);

                if (!validation.valid) {
                    console.error('Mensaje de tarjeta inválido:', validation.error);
                    return;
                }

                animateToChatMode();
                addMessage(msg, true);
                sendMessage(msg);
            });
        });
    }

    // Limpiar chat (usado por el botón "Nuevo chat" y "Limpiar chat")
    window.addEventListener('clearChat', () => {
        chatMessages.innerHTML = '';
        hideChatBox();
        // Restaurar tarjetas y input
        if (mainCards) {
            mainCards.classList.remove('hidden', 'opacity-0', 'pointer-events-none');
            setTimeout(() => mainCards.classList.remove('opacity-0', 'pointer-events-none'), 10);
        }
        if (mainInputBox) {
            mainInputBox.className = 'w-full max-w-2xl flex gap-2 px-2 mb-8 transition-all duration-500 justify-center';
            mainInputBox.style.maxWidth = '';
        }
    });
}); 