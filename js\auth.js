// Sistema de autenticación temporal
class AuthManager {
    constructor() {
        this.isLoggedIn = false;
        this.userPhone = null;
        this.init();
    }

    init() {
        // Verificar si ya está logueado con JWT válido
        const savedAuth = localStorage.getItem('hazlolatam_auth');
        if (savedAuth) {
            try {
                const authData = JSON.parse(savedAuth);
                if (authData.token && authData.phone) {
                    this.isLoggedIn = true;
                    this.userPhone = authData.phone;
                    this.jwtToken = authData.token;
                    console.log('Sesión restaurada para:', authData.phone);
                }
            } catch (error) {
                console.error('Error al restaurar sesión:', error);
                // Limpiar datos corruptos
                localStorage.removeItem('hazlolatam_auth');
            }
        }

        this.updateUI();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Botón de login en sidebar
        const loginBtn = document.getElementById('login-btn');
        if (loginBtn) {
            loginBtn.addEventListener('click', () => this.showLoginModal());
        }

        // Botón de logout
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.logout());
        }

        // Modal de login
        const loginModal = document.getElementById('login-modal');
        const loginForm = document.getElementById('login-form');
        const cancelLogin = document.getElementById('cancel-login');

        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        if (cancelLogin) {
            cancelLogin.addEventListener('click', () => this.hideLoginModal());
        }

        // Cerrar modal al hacer click fuera
        if (loginModal) {
            loginModal.addEventListener('click', (e) => {
                if (e.target === loginModal) {
                    this.hideLoginModal();
                }
            });
        }

        // Modal de OTP
        const otpModal = document.getElementById('otp-modal');
        const otpForm = document.getElementById('otp-form');
        const cancelOTP = document.getElementById('cancel-otp');
        const resendOTP = document.getElementById('resend-otp');

        if (otpForm) {
            otpForm.addEventListener('submit', (e) => this.handleOTPVerification(e));
        }

        if (cancelOTP) {
            cancelOTP.addEventListener('click', () => {
                this.hideOTPModal();
                this.showLoginModal(); // Volver al modal de login
            });
        }

        if (resendOTP) {
            resendOTP.addEventListener('click', () => this.resendOTP());
        }

        // Cerrar modal de OTP al hacer click fuera
        if (otpModal) {
            otpModal.addEventListener('click', (e) => {
                if (e.target === otpModal) {
                    this.hideOTPModal();
                    this.showLoginModal(); // Volver al modal de login
                }
            });
        }

        // Interceptar botones que requieren autenticación
        this.interceptAuthRequiredActions();
    }

    interceptAuthRequiredActions() {
        // Interceptar envío de mensajes
        const sendButton = document.getElementById('send-button');
        const messageInput = document.getElementById('message-input');
        
        if (sendButton) {
            sendButton.addEventListener('click', (e) => {
                if (!this.isLoggedIn) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showLoginModal();
                    return false;
                }
            }, true); // Usar capture para interceptar antes
        }

        if (messageInput) {
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !this.isLoggedIn) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showLoginModal();
                    return false;
                }
            }, true);
        }

        // Interceptar tarjetas de chat
        document.addEventListener('click', (e) => {
            const chatCard = e.target.closest('button[data-msg]');
            if (chatCard && !this.isLoggedIn) {
                e.preventDefault();
                e.stopPropagation();
                this.showLoginModal();
                return false;
            }
        }, true);

        // Interceptar enlaces rápidos
        document.addEventListener('click', (e) => {
            const quickLink = e.target.closest('.quick-link');
            if (quickLink && !this.isLoggedIn) {
                e.preventDefault();
                e.stopPropagation();
                this.showLoginModal();
                return false;
            }
        }, true);
    }

    showLoginModal() {
        const modal = document.getElementById('login-modal');
        if (modal) {
            modal.classList.remove('hidden');
            // Focus en el input
            setTimeout(() => {
                const phoneInput = document.getElementById('phone-number');
                if (phoneInput) phoneInput.focus();
            }, 100);
        }
    }

    hideLoginModal() {
        const modal = document.getElementById('login-modal');
        if (modal) {
            modal.classList.add('hidden');
            // Limpiar el formulario
            const form = document.getElementById('login-form');
            if (form) form.reset();
        }
    }

    async handleLogin(e) {
        e.preventDefault();

        const phoneInput = document.getElementById('phone-number');
        const submitBtn = e.target.querySelector('button[type="submit"]');
        let phone = phoneInput.value.trim();

        // Validación básica
        if (!phone || phone.length < 10) {
            alert('Por favor ingresa un número de teléfono válido');
            return;
        }

        // Asegurar que el número tenga el formato correcto con +
        if (!phone.startsWith('+')) {
            phone = '+' + phone;
        }

        // Deshabilitar botón y mostrar loading
        submitBtn.disabled = true;
        submitBtn.textContent = 'Enviando...';

        try {
            // Llamar a la API para enviar OTP
            const response = await fetch('https://auth-wasap-api-production.up.railway.app/api/auth/request-otp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    whatsappNumber: phone
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                // OTP enviado exitosamente, mostrar modal de verificación
                this.showOTPModal(phone);
            } else {
                // Error al enviar OTP
                alert(data.message || 'Error al enviar el código. Intenta de nuevo.');
            }
        } catch (error) {
            console.error('Error al enviar OTP:', error);
            alert('Error de conexión. Verifica tu internet e intenta de nuevo.');
        } finally {
            // Restaurar botón
            submitBtn.disabled = false;
            submitBtn.textContent = 'Continuar';
        }
    }

    showOTPModal(phone) {
        // Ocultar modal de login
        this.hideLoginModal();

        // Mostrar modal de OTP
        const otpModal = document.getElementById('otp-modal');
        if (otpModal) {
            otpModal.classList.remove('hidden');
            // Actualizar número en el modal
            const phoneDisplay = document.getElementById('otp-phone-display');
            if (phoneDisplay) {
                phoneDisplay.textContent = phone;
            }
            // Focus en el input de OTP
            setTimeout(() => {
                const otpInput = document.getElementById('otp-code');
                if (otpInput) otpInput.focus();
            }, 100);
        }

        // Guardar número temporalmente
        this.tempPhone = phone;
    }

    hideOTPModal() {
        const modal = document.getElementById('otp-modal');
        if (modal) {
            modal.classList.add('hidden');
            // Limpiar el formulario
            const form = document.getElementById('otp-form');
            if (form) form.reset();
        }
    }

    async handleOTPVerification(e) {
        e.preventDefault();

        const otpInput = document.getElementById('otp-code');
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const otpCode = otpInput.value.trim();

        // Validación básica
        if (!otpCode || otpCode.length < 4) {
            alert('Por favor ingresa el código de verificación');
            return;
        }

        // Deshabilitar botón y mostrar loading
        submitBtn.disabled = true;
        submitBtn.textContent = 'Verificando...';

        try {
            // Llamar a la API para verificar OTP
            const response = await fetch('https://auth-wasap-api-production.up.railway.app/api/auth/verify-otp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    whatsappNumber: this.tempPhone,
                    otp: otpCode
                })
            });

            const data = await response.json();

            if (response.ok && data.success && data.token) {
                // OTP verificado exitosamente, login con JWT
                this.loginWithJWT(this.tempPhone, data.token);
            } else {
                // Error en la verificación
                alert(data.message || 'Código incorrecto. Intenta de nuevo.');
            }
        } catch (error) {
            console.error('Error al verificar OTP:', error);
            alert('Error de conexión. Verifica tu internet e intenta de nuevo.');
        } finally {
            // Restaurar botón
            submitBtn.disabled = false;
            submitBtn.textContent = 'Verificar';
        }
    }

    loginWithJWT(phone, token) {
        this.isLoggedIn = true;
        this.userPhone = phone;
        this.jwtToken = token;

        // Guardar en localStorage con JWT
        localStorage.setItem('hazlolatam_auth', JSON.stringify({
            phone: phone,
            token: token,
            loginTime: new Date().toISOString()
        }));

        // Actualizar UI
        this.updateUI();
        this.hideOTPModal();

        // Mostrar mensaje de bienvenida
        console.log(`¡Bienvenido! Número: ${phone}`);

        // Limpiar número temporal
        this.tempPhone = null;
    }

    login(phone) {
        // Método legacy - ahora redirige al flujo de OTP
        this.showLoginModal();
    }

    async resendOTP() {
        if (!this.tempPhone) {
            alert('Error: No hay número de teléfono para reenviar');
            return;
        }

        const resendBtn = document.getElementById('resend-otp');
        if (resendBtn) {
            resendBtn.disabled = true;
            resendBtn.textContent = 'Reenviando...';
        }

        try {
            const response = await fetch('https://auth-wasap-api-production.up.railway.app/api/auth/request-otp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    whatsappNumber: this.tempPhone
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                alert('Código reenviado exitosamente');
            } else {
                alert(data.message || 'Error al reenviar el código');
            }
        } catch (error) {
            console.error('Error al reenviar OTP:', error);
            alert('Error de conexión al reenviar');
        } finally {
            if (resendBtn) {
                resendBtn.disabled = false;
                resendBtn.textContent = 'Reenviar código';
            }
        }
    }

    logout() {
        if (confirm('¿Estás seguro de que quieres cerrar sesión?')) {
            this.isLoggedIn = false;
            this.userPhone = null;
            this.jwtToken = null;
            this.tempPhone = null;

            // Limpiar localStorage
            localStorage.removeItem('hazlolatam_auth');

            // Limpiar chat
            window.dispatchEvent(new Event('clearChat'));

            // Actualizar UI
            this.updateUI();
        }
    }

    updateUI() {
        const loggedInContent = document.getElementById('logged-in-content');
        const loggedOutContent = document.getElementById('logged-out-content');
        const loginBtn = document.getElementById('login-btn');
        const logoutBtn = document.getElementById('logout-btn');

        if (this.isLoggedIn) {
            // Mostrar contenido de usuario logueado
            if (loggedInContent) loggedInContent.classList.remove('hidden');
            if (loggedOutContent) loggedOutContent.classList.add('hidden');
            if (loginBtn) loginBtn.classList.add('hidden');
            if (logoutBtn) logoutBtn.classList.remove('hidden');
        } else {
            // Mostrar contenido de usuario no logueado
            if (loggedInContent) loggedInContent.classList.add('hidden');
            if (loggedOutContent) loggedOutContent.classList.remove('hidden');
            if (loginBtn) loginBtn.classList.remove('hidden');
            if (logoutBtn) logoutBtn.classList.add('hidden');
        }
    }

    // Método público para verificar autenticación
    checkAuth() {
        return this.isLoggedIn;
    }

    // Método público para obtener datos del usuario
    getUserData() {
        return {
            isLoggedIn: this.isLoggedIn,
            phone: this.userPhone,
            token: this.jwtToken
        };
    }

    // Método público para obtener el JWT para solicitudes autenticadas
    getAuthToken() {
        return this.jwtToken;
    }

    // Método público para obtener headers de autenticación
    getAuthHeaders() {
        if (this.jwtToken) {
            return {
                'Authorization': `Bearer ${this.jwtToken}`,
                'Content-Type': 'application/json'
            };
        }
        return {
            'Content-Type': 'application/json'
        };
    }
}

// Inicializar el sistema de autenticación cuando se carga la página
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});
