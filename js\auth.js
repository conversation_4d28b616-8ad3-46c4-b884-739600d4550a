// Sistema de autenticación temporal
class AuthManager {
    constructor() {
        this.isLoggedIn = false;
        this.userPhone = null;
        this.init();
    }

    init() {
        // Verificar si ya está logueado
        const savedAuth = localStorage.getItem('hazlolatam_auth');
        if (savedAuth) {
            const authData = JSON.parse(savedAuth);
            this.isLoggedIn = true;
            this.userPhone = authData.phone;
            this.updateUI();
        } else {
            this.updateUI();
        }

        // Event listeners
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Botón de login en sidebar
        const loginBtn = document.getElementById('login-btn');
        if (loginBtn) {
            loginBtn.addEventListener('click', () => this.showLoginModal());
        }

        // Botón de logout
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.logout());
        }

        // Modal de login
        const loginModal = document.getElementById('login-modal');
        const loginForm = document.getElementById('login-form');
        const cancelLogin = document.getElementById('cancel-login');

        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        if (cancelLogin) {
            cancelLogin.addEventListener('click', () => this.hideLoginModal());
        }

        // Cerrar modal al hacer click fuera
        if (loginModal) {
            loginModal.addEventListener('click', (e) => {
                if (e.target === loginModal) {
                    this.hideLoginModal();
                }
            });
        }

        // Interceptar botones que requieren autenticación
        this.interceptAuthRequiredActions();
    }

    interceptAuthRequiredActions() {
        // Interceptar envío de mensajes
        const sendButton = document.getElementById('send-button');
        const messageInput = document.getElementById('message-input');
        
        if (sendButton) {
            sendButton.addEventListener('click', (e) => {
                if (!this.isLoggedIn) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showLoginModal();
                    return false;
                }
            }, true); // Usar capture para interceptar antes
        }

        if (messageInput) {
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !this.isLoggedIn) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showLoginModal();
                    return false;
                }
            }, true);
        }

        // Interceptar tarjetas de chat
        document.addEventListener('click', (e) => {
            const chatCard = e.target.closest('button[data-msg]');
            if (chatCard && !this.isLoggedIn) {
                e.preventDefault();
                e.stopPropagation();
                this.showLoginModal();
                return false;
            }
        }, true);

        // Interceptar enlaces rápidos
        document.addEventListener('click', (e) => {
            const quickLink = e.target.closest('.quick-link');
            if (quickLink && !this.isLoggedIn) {
                e.preventDefault();
                e.stopPropagation();
                this.showLoginModal();
                return false;
            }
        }, true);
    }

    showLoginModal() {
        const modal = document.getElementById('login-modal');
        if (modal) {
            modal.classList.remove('hidden');
            // Focus en el input
            setTimeout(() => {
                const phoneInput = document.getElementById('phone-number');
                if (phoneInput) phoneInput.focus();
            }, 100);
        }
    }

    hideLoginModal() {
        const modal = document.getElementById('login-modal');
        if (modal) {
            modal.classList.add('hidden');
            // Limpiar el formulario
            const form = document.getElementById('login-form');
            if (form) form.reset();
        }
    }

    handleLogin(e) {
        e.preventDefault();
        
        const phoneInput = document.getElementById('phone-number');
        const phone = phoneInput.value.trim();

        // Validación básica
        if (!phone || phone.length < 10) {
            alert('Por favor ingresa un número de teléfono válido');
            return;
        }

        // Simular login exitoso
        this.login(phone);
    }

    login(phone) {
        this.isLoggedIn = true;
        this.userPhone = phone;
        
        // Guardar en localStorage
        localStorage.setItem('hazlolatam_auth', JSON.stringify({
            phone: phone,
            loginTime: new Date().toISOString()
        }));

        // Actualizar UI
        this.updateUI();
        this.hideLoginModal();

        // Mostrar mensaje de bienvenida
        console.log(`Bienvenido! Número: ${phone}`);
    }

    logout() {
        if (confirm('¿Estás seguro de que quieres cerrar sesión?')) {
            this.isLoggedIn = false;
            this.userPhone = null;
            
            // Limpiar localStorage
            localStorage.removeItem('hazlolatam_auth');
            
            // Limpiar chat
            window.dispatchEvent(new Event('clearChat'));
            
            // Actualizar UI
            this.updateUI();
        }
    }

    updateUI() {
        const loggedInContent = document.getElementById('logged-in-content');
        const loggedOutContent = document.getElementById('logged-out-content');
        const loginBtn = document.getElementById('login-btn');
        const logoutBtn = document.getElementById('logout-btn');

        if (this.isLoggedIn) {
            // Mostrar contenido de usuario logueado
            if (loggedInContent) loggedInContent.classList.remove('hidden');
            if (loggedOutContent) loggedOutContent.classList.add('hidden');
            if (loginBtn) loginBtn.classList.add('hidden');
            if (logoutBtn) logoutBtn.classList.remove('hidden');
        } else {
            // Mostrar contenido de usuario no logueado
            if (loggedInContent) loggedInContent.classList.add('hidden');
            if (loggedOutContent) loggedOutContent.classList.remove('hidden');
            if (loginBtn) loginBtn.classList.remove('hidden');
            if (logoutBtn) logoutBtn.classList.add('hidden');
        }
    }

    // Método público para verificar autenticación
    checkAuth() {
        return this.isLoggedIn;
    }

    // Método público para obtener datos del usuario
    getUserData() {
        return {
            isLoggedIn: this.isLoggedIn,
            phone: this.userPhone
        };
    }
}

// Inicializar el sistema de autenticación cuando se carga la página
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});
